import moment from "moment";
import { State } from "country-state-city";
import { Booking } from "./bookingModal.js";
import {
  createBookingEvents,
  deleteSingleEvent,
} from "../calendar/calendarController.js";
import { v4 as uuidv4 } from "uuid";
import { Course } from "../courses/courseModal.js";
import { daysDifference, formatDateToYYYYMMDD } from "../utils/datehelper.js";
import { Player } from "../player/playerModal.js";
import { updateBalance } from "../wallet/walletController.js";
import { razorpayRefund } from "../razorpay/razorpayController.js";
import { Wallet } from "../wallet/walletModal.js";
import { DraftBooking } from "./draftBookingModal.js";
import { Events } from "../events/eventModal.js";
import mongoose from "mongoose";
import { sendEmail, sendSms } from "../utils/msg.js";
import { Parser } from "json2csv";
import { PassThrough } from "stream";
import PDFDocument from "pdfkit";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";
import AWS from "aws-sdk";
import { SECRETS } from "../utils/config.js";
import { Coach } from "../coaches/coachModal.js";
import { Academy } from "../academyModule/academy/academyModel.js";

const AWS_ACCESS_KEY_ID = SECRETS.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = SECRETS.AWS_SECRET_ACCESS_KEY;
const AWS_BUCKET_NAME = SECRETS.AWS_BUCKET_NAME;
const AWS_S3_REGION = SECRETS.AWS_S3_REGION;

AWS.config.update({
  accessKeyId: AWS_ACCESS_KEY_ID,
  secretAccessKey: AWS_SECRET_ACCESS_KEY,
  region: AWS_S3_REGION,
});

const s3 = new AWS.S3();
export const getBookings = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let {
      page,
      courseType,
      playerId,
      coachId,
      courseId,
      status,
      startDate,
      endDate,
      courseName,
    } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};
    if (req.userType === "academy" && req.user?.academyId?._id) {
      query.academyId = req.user.academyId._id;
    }
    if (courseName) {
      query.courseName = { $regex: courseName, $options: "i" };
    }

    if (courseType) {
      query.courseType = courseType;
    }

    if (playerId) {
      query.player = playerId;
    }

    if (coachId) {
      query.coachId = coachId;
    }

    if (courseId) {
      query.courseId = courseId;
    }

    if (status) {
      query.status = status;
    }

    if (startDate && endDate) {
      const start = moment
        .tz(startDate, "Asia/Kolkata")
        .startOf("day")
        .toDate(); // Set to 12:00 AM of startDate
      const end = moment.tz(endDate, "Asia/Kolkata").endOf("day").toDate(); // Set to 11:59:59 PM of endDate

      query.createdAt = {
        $gte: start,
        $lte: end,
      };
    }
    const totalResults = await Booking.countDocuments(query);
    const data = await Booking.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .populate(["coachId", "courseId", "player", "academyId"])
      .sort({ createdAt: -1 });

    if (data.length === 0) {
      return res.status(200).json({ message: "No bookings found" });
    }

    res.status(200).json({
      data,
      totalResults,
      currentPage: page,
      totalPages: Math.ceil(totalResults / limitVal),
    });
  } catch (e) {
    console.error(e);
    res.status(500).json({ error: "Internal server error" });
  }
};

export const getBookingDetailsById = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(404).json({ error: "Id is required" });
    }
    const data = await Booking.findById(id).populate([
      "coachId",
      "courseId",
      "player",
      "academyId",
    ]);
    if (!data) {
      return res.status(400).json({ error: "Booking not found" });
    }
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const createDraftBooking = async (
  params,
  orderId,
  wallet,
  walletAmount
) => {
  try {
    let {
      coachId,
      courseId,
      player,
      courseType,
      classes,
      groupSize,
      pricePaid,
    } = params;
    const bookingId = uuidv4().replace(/-/g, "").slice(0, 8);
    // Validate bookingId uniqueness
    const isBookingIdUnique = await Booking.findOne({ bookingId });
    if (isBookingIdUnique) {
      return {
        status: 400,
        error: "Booking ID must be unique",
      };
    }

    // Validate required fields
    if (
      !coachId ||
      !courseId ||
      !groupSize ||
      !pricePaid ||
      !player ||
      !courseType
    ) {
      return {
        status: 400,
        error:
          "coachId, courseId, groupSize, player, courseType, and pricePaid are required fields",
      };
    }
    const coachForAcademy = await Coach.findById(coachId);
    let academyId = null;
    if (coachForAcademy?.affiliationType === "academy") {
      academyId = coachForAcademy?.academyId;
    }
    // Validate class booking with optional classes and multiple players
    const fCourse = await Course.findById(courseId);
    if (courseType === "course") {
      if (fCourse.playerEnrolled === fCourse.maxGroupSize) {
        return {
          status: 400,
          error: "Max group size reached",
        };
      }
    }
    const classesArray = await generateClassesArray(
      courseId,
      courseType,
      classes,
      coachId
    );
    //  here set classes equal to the dates of courses
    if (classesArray) {
      if (!Array.isArray(classesArray) || classesArray.length < 1) {
        return {
          status: 400,
          error:
            "For class type, dates array should be an array with at least 1 date",
        };
      }
      // Validate each date
      const isValidDates = classesArray.every(
        (dateObj) =>
          moment(dateObj.date).isValid() &&
          moment(dateObj.startTime, "HH:mm", true).isValid() &&
          moment(dateObj.endTime, "HH:mm", true).isValid() &&
          moment(dateObj.endTime, "HH:mm", true).isSameOrAfter(
            moment(dateObj.startTime, "HH:mm", true),
            "minutes"
          ) &&
          typeof dateObj.duration === "string" &&
          typeof dateObj.days === "string" && // Change here to check for string
          ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].includes(
            dateObj.days
          )
      );

      if (!isValidDates) {
        return {
          status: 400,
          error:
            "Invalid date format or duration in the dates array for class type",
        };
      }

      const playerObj = await Player.findById(player);
      const course = await Course.findById(courseId);
      let classesArrayUTC;
      if (courseType === "class") {
        classesArrayUTC = classesArray.map((classObj) => ({
          ...classObj,
          // date: moment.tz(classObj.date, "Asia/Kolkata").utc().format(),
          date: classObj.date.slice(0, 10),
          invoice: `INV-${uuidv4().replace(/-/g, "").slice(0, 8)}`,
        }));
      } else {
        classesArrayUTC = classesArray;
      }
      const bookingData = {
        ...params,
        bookingId,
        orderId,
        wallet,
        walletAmount: walletAmount,
        classes: classesArrayUTC,
        courseName: course.courseName,
        playerName: playerObj.firstName,
        playerEmail: playerObj.email,
        academyId: academyId ? academyId : null,
      };

      if (academyId) {
        bookingData.academyId = academyId;
      }

      const data = await DraftBooking.create(bookingData);
      return { status: 200, data };
    }
  } catch (e) {
    console.log(e);
    return {
      status: 500,
      error: "Internal server error",
    };
  }
};
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export const generateOrderSummaryPDF = async (params) => {
  const coach = await Coach.findById(params.coachId).populate(
    "academyId",
    "platformFee"
  );
  const player = await Player.findById(params.player);
  const courseDe = await Course.findById(params.courseId);

  // Get all states of India
  const allStates = State.getStatesOfCountry("IN");
  const facilityState = allStates.find(
    (state) => state.name === courseDe.facility.state
  );
  const facilityStateIsoCode = facilityState?.isoCode;
  const doc = new PDFDocument({ margin: 50 });
  const registeredState = "DL";
  const fileName = `order-summary-${params?.bookingId}.pdf`;
  const total = params.pricePaid;
  let platformFeePercentage = (process.env.PLATFORM_FEE / 100).toFixed(2);
  if (coach.academyId) {
    platformFeePercentage = coach.academyId.platformFee / 100;
  }
  const gstPercentage = 0.18;
  const coachGstPercentage = 0.18;

  let subtotal = params?.classes?.reduce((accu, x) => accu + x.fees, 0);
  subtotal = coach.hasGst ? subtotal / 1.18 : subtotal;
  const platformTax = subtotal * platformFeePercentage;
  const playerIGST = platformTax * 0.18;
  // Calculate other components
  const platformFee = subtotal * platformFeePercentage;
  const gstOnPlatformFee = platformFee * gstPercentage;
  const coachGst = coach?.hasGst ? subtotal * 0.18 : 0;
  const totalTaxes = gstOnPlatformFee + coachGst;

  // Amount breakdown
  let subtotalTwo = params?.classes?.reduce((accu, x) => accu + x.fees, 0);
  subtotalTwo = coach.hasGst ? subtotalTwo / 1.18 : subtotalTwo;
  const taxGST = coach.hasGst ? subtotalTwo * 0.18 : 0;
  let from =
    "Umn Khel Shiksha Private Limited,Vasant Vihar,Basant Lok Complex,Road 21,New Delhi-110057";
  let gstid = "07AADCU2822L1Z8";
  // Create a PassThrough stream to write the PDF content directly to S3
  const passThroughStream = new PassThrough();

  // S3 upload parameters
  const s3Params = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: `pdfs/${fileName}`,
    Body: passThroughStream,
    ContentType: "application/pdf",
  };

  // Start the S3 upload
  const s3Upload = s3.upload(s3Params).promise();

  // Pipe the PDF content to the PassThrough stream
  doc.pipe(passThroughStream);

  // Adding content to the PDF
  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text("Khel-Coach Order-Summary", { align: "left" });
  doc.moveDown();

  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text(`Course Name: ${params.courseName}`, { align: "left" });
  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text(`Invoice No: ${params.bookingId}`, { align: "right" });
  doc.moveDown();
  doc
    .fontSize(12)
    .font("Helvetica")
    .text(
      `Order Date: ${new Date(params.bookingDate).toLocaleString("en-IN", {
        timeZone: "Asia/Kolkata",
        timeZoneName: "short",
      })}`,
      { align: "left" }
    );

  doc.moveDown(2);
  // Drawing table headers
  doc
    .fontSize(10)
    .font("Helvetica-Bold")
    .text("DESCRIPTION", 50, doc.y, { continued: true });
  doc.text("AMOUNT (Rs)", 400);
  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  // Adding the content to the table
  doc.moveDown();
  doc
    .fontSize(10)
    .font("Helvetica")
    .text("Subtotal (class)", 50, doc.y, { continued: true });
  doc.text(`${subtotal.toFixed(2)}`, 400);
  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  doc.moveDown();
  doc.text(`Platform Fee (${platformFeePercentage * 100}%)`, 50, doc.y, {
    continued: true,
  });
  doc.text(`${platformTax.toFixed(2)}`, 382);
  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  doc.moveDown();
  doc.text("Total Taxes", 50, doc.y, { continued: true });
  doc.text(`${(playerIGST + (coachGst || 0))?.toFixed(2)}`, 445);
  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  doc.moveDown();
  doc
    .font("Helvetica-Bold")
    .text("TOTAL", 50, doc.y, { continued: true, color: "red" });
  doc.text(`${params.pricePaid.toFixed(2)}`, 436);
  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  doc.moveDown(2);
  // Add a new page for additional information (Page 2)
  doc.addPage();

  // Adding content to the PDF
  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text("Khel-Coach Platform Invoice", { align: "left" });
  doc.moveDown();

  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text("From: ", { continued: true })
    .font("Helvetica")
    .text(from);

  doc.moveDown();

  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text("To: ", { continued: true })
    .font("Helvetica")
    .text(`${params.playerName} ${player.lastName}`);
  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text("Email: ", { continued: true })
    .font("Helvetica")
    .text(`${params.playerEmail}`);
  doc.moveDown();
  doc
    .fontSize(12)
    .font("Helvetica")
    .text(
      `Order Date: ${new Date(params.bookingDate).toLocaleString("en-IN", {
        timeZone: "Asia/Kolkata",
        timeZoneName: "short",
      })}`,
      { align: "left" }
    );

  doc.moveDown(2);
  // Drawing table headers
  doc
    .fontSize(10)
    .font("Helvetica-Bold")
    .text("DESCRIPTION", 50, doc.y, { continued: true });
  doc.text("AMOUNT (Rs)", 400);
  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  // Adding the content to the table
  doc.moveDown();
  doc
    .fontSize(10)
    .font("Helvetica")
    .text("Service Fess", 50, doc.y, { continued: true });
  doc.text(`${platformTax.toFixed(2)}`, 410);

  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  if (player.homeState !== registeredState) {
    // Show IGST
    doc.moveDown();
    doc.text("IGST (18%)", 50, doc.y, { continued: true });
    doc.text(`${playerIGST.toFixed(2)}`, 430);
    doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();
  } else {
    // Show CGST and SGST (each 9%)
    const cgst = playerIGST / 2;
    const sgst = playerIGST / 2;

    doc.moveDown();
    doc.text("CGST (9%)", 50, doc.y, { continued: true });
    doc.text(`${cgst.toFixed(2)}`, 420);
    doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

    doc.moveDown();
    doc.text("SGST (9%)", 50, doc.y, { continued: true });
    doc.text(`${sgst.toFixed(2)}`, 420);
    doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();
  }

  doc.moveDown();
  doc
    .font("Helvetica-Bold")
    .text("TOTAL", 50, doc.y, { continued: true, color: "red" });
  doc.text(`${Math.ceil(platformTax + platformTax * 0.18).toFixed(2)}`, 433);

  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  doc.moveDown(2);

  // Add a new page for terms and conditions (Page 3)
  doc.addPage();

  // Adding content to the PDF
  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text("Khel-Coach Caoch Invoice", { align: "left" });
  doc.moveDown();

  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text("From: ", { continued: true })
    .font("Helvetica")
    .text(
      `Umn Khel Shiksha Private Limited on behalf of ${coach.firstName} ${coach.lastName}`
    );

  doc.moveDown();

  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text("To: ", { continued: true })
    .font("Helvetica")
    .text(`${params.playerName} ${player.lastName}`);
  doc
    .fontSize(12)
    .font("Helvetica-Bold")
    .text("Email: ", { continued: true })
    .font("Helvetica")
    .text(`${params.playerEmail}`);

  doc.moveDown();
  doc
    .fontSize(12)
    .font("Helvetica")
    .text(
      `Order Date: ${new Date(params.bookingDate).toLocaleString("en-IN", {
        timeZone: "Asia/Kolkata",
        timeZoneName: "short",
      })}`,
      { align: "left" }
    );

  doc.moveDown(2);
  // Drawing table headers
  doc
    .fontSize(10)
    .font("Helvetica-Bold")
    .text("DESCRIPTION", 50, doc.y, { continued: true });
  doc.text("AMOUNT (Rs)", 400);
  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  // Adding the content to the table
  doc.moveDown();
  doc
    .fontSize(10)
    .font("Helvetica")
    .text("Basic Price", 50, doc.y, { continued: true });
  doc.text(`${subtotalTwo.toFixed(2)}`, 415);

  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  // if (coach?.hasGst === false) {
  //   // Show IGST when the coach does not have GST registration
  //   doc.moveDown();
  //   doc.text("IGST (18%)", 50, doc.y, { continued: true });
  //   doc.text(`${Math.ceil(taxGST).toFixed(2)}`, 420);
  //   doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();
  // } else {
  // Compare coach's GST state (ISO code) with facility's state (ISO code)
  if (coach.gstState === facilityStateIsoCode) {
    // If states match, show CGST and SGST (9% each)
    const cgst = taxGST / 2;
    const sgst = taxGST / 2;

    doc.moveDown();
    doc.text("CGST (9%)", 50, doc.y, { continued: true });
    doc.text(`${Math.ceil(cgst).toFixed(2)}`, 420);
    doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

    doc.moveDown();
    doc.text("SGST (9%)", 50, doc.y, { continued: true });
    doc.text(`${Math.ceil(sgst).toFixed(2)}`, 420);
    doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();
  } else if (
    coach.gstState !== facilityStateIsoCode &&
    coach?.hasGst === true
  ) {
    // If states don't match, show IGST (18%)
    doc.moveDown();
    doc.text("IGST (18%)", 50, doc.y, { continued: true });
    doc.text(`${Math.ceil(taxGST).toFixed(2)}`, 420);
    doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();
  }
  // }

  doc.moveDown();
  doc
    .font("Helvetica-Bold")
    .text("TOTAL", 50, doc.y, { continued: true, color: "red" });
  doc.text(`${(subtotal + taxGST).toFixed(2)}`, 433);

  doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

  doc.moveDown(2);

  doc.moveDown();

  // Finalize PDF
  doc.end();

  try {
    // Wait for the S3 upload to finish
    const s3UploadResult = await s3Upload;
    const pdfUrl = s3UploadResult.Location;
    return pdfUrl;
  } catch (error) {
    console.error("Error uploading PDF to S3:", error);
    throw error;
  }
};
export const createBooking = async (params) => {
  try {
    // if (params.courseType === "course") {
    await Course.findByIdAndUpdate(
      { _id: params?.courseId },
      { $inc: { playerEnrolled: 1 } },
      { new: true }
    );
    // }

    const player = await Player.findOne({
      _id: params.player,
      $or: [
        { privacyPolicyAccepted: { $exists: false } },
        { privacyPolicyAccepted: false },
      ],
    });
    if (player) {
      await Player.findByIdAndUpdate(params.player, {
        privacyPolicyAccepted: true,
      });
    }

    const coachForAcademy = await Coach.findById(params?.coachId);
    let academyId = null;
    if (coachForAcademy?.affiliationType === "academy") {
      academyId = coachForAcademy?.academyId;
    }

    const data = await Booking.create({
      bookingId: params?.bookingId,
      orderId: params?.orderId ? params.orderId : null,
      academyId: academyId ? academyId : null,
      coachId: params?.coachId,
      courseId: params?.courseId,
      courseName: params?.courseName,
      player: params?.player,
      playerName: params?.playerName,
      playerEmail: params?.playerEmail,
      courseType: params?.courseType,
      classes: params?.classes,
      groupSize: params?.groupSize,
      pricePaid: params?.pricePaid,
      razorPayPaymentId: params?.razorPayPaymentId
        ? params.razorPayPaymentId
        : null,
      paymentMode: params?.paymentMode ? params.paymentMode : null,
      paymentId: params?.paymentId ? params.paymentId : null,
      status: params?.status,
      bookingDate: params?.bookingDate,
      paymentStatus: params?.paymentStatus,
      wallet: params?.wallet,
      walletAmount: params?.walletAmount,
    });
    params.classes.map(async (details, index) => {
      const dateTimeString = `${formatDateToYYYYMMDD(new Date(details.date))}T${
        details.startTime
      }:00`;
      const endDate = `${formatDateToYYYYMMDD(new Date(details.date))}T${
        details.endTime
      }:00`;
      const calendarBooking = await createBookingEvents({
        summary: params.playerName,
        startDateTime: dateTimeString,
        endDateTime: endDate,
        description: JSON.stringify({ id: data._id, type: "bookings" }),
        days: [details.days],
        daysCount: daysDifference(dateTimeString, endDate, [details.days]),
        coachId: params.coachId,
        colorId: "3",
      });
      if (calendarBooking.status === 200) {
        // Update eventId for the specific class
        const eventIdToUpdate = calendarBooking.data.data.id;
        await Booking.findByIdAndUpdate(
          data._id,
          { $set: { [`classes.${index}.eventId`]: eventIdToUpdate } },
          { new: true }
        );
      }
    });
    if (params.wallet && params.walletAmount > 0) {
      const updateBalanceResult = await updateBalance(
        params.player,
        "debit",
        params.courseId,
        params.playerEmail,
        -params.walletAmount
      );
      if (updateBalanceResult.status !== 200) {
        return res.status(400).json({ error: updateBalanceResult.error });
      }
    }

    const pdfFilePath = await generateOrderSummaryPDF(params);

    // Prepare variables for the email template
    const course = await Course.findById(params.courseId);
    let priceForCoach = 0;
    params.classes.forEach((cls) => {
      priceForCoach += cls.fees;
    });
    const coach = await Coach.findOne({ _id: params.coachId });
    const getPlayer = await Player.findOne({ _id: params.player });
    //send booking sms confirmation
    const formattedDate = new Date(params.bookingDate).toLocaleString("en-IN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
      timeZone: "Asia/Kolkata",
    });
    await sendSms(
      "6791e6b4d6fc052a565aa293",
      "4",
      getPlayer.mobile,
      `${coach.firstName} ${coach.lastName}`,
      formattedDate,
      params.pricePaid
    );

    //send sms booking confirmation to the coach
    await sendSms(
      "6791e5ddd6fc055f752d3cc2",
      "4",
      coach.mobile,
      `${getPlayer.firstName} ${getPlayer.lastName}`,
      formattedDate,
      null
    );
    // for player
    await sendEmail(
      params?.playerName,
      params?.playerEmail,
      "booking_confirmation_for_the_player",
      `{"Player": "${params.playerName}", "course":"${params?.courseName}", "Location":"${course?.facility.name}", "Amount":"${params.pricePaid}"}`,
      pdfFilePath
    );
    // for coach
    await sendEmail(
      course?.coachName,
      course?.coachEmail,
      "booking_confirmation_for_the_coach",
      `{"Player": "${params.playerName}","Coach":"${course?.coachName}", "course_type":"${course.classType}", "Location":"${course?.facility.name}", "Amount":"${priceForCoach}"}`
    );
    return { status: 200, data };
  } catch (e) {
    console.log(e);
    return {
      status: 500,
      error: "Internal server error",
    };
  }
};

// find the slots for booking
export const getBookingSlots = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { courseId, startDate, endDate, coachId } = req.body;
    const courseBookings = await Booking.find({
      courseId,
      "classes.date": { $gte: startDate, $lt: endDate },
    });
    let bookingClass = [];
    courseBookings.forEach((booking) => {
      booking.classes.forEach((cls) => {
        bookingClass.push({
          bookingId: booking._id,
          booking: booking.bookingId,
          orderId: booking.orderId,
          coachId: booking.coachId,
          courseId: booking.courseId,
          courseName: booking.courseName,
          player: booking.player,
          playerName: booking.playerName,
          playerEmail: booking.playerEmail,
          courseType: booking.courseType,
          classId: cls._id,
          classDate: cls.date,
          classStartTime: cls.startTime,
          classEndTime: cls.endTime,
          duration: cls.duration,
          days: cls.days,
          fees: cls.fees,
          status: cls.status,
          cancellationDate: cls.cancellationDate,
          eventId: cls.eventId,
          attendance: cls.attendance,
        });
      });
    });
    const filteredClasses = bookingClass.filter(
      (cls) =>
        cls.classDate.toISOString().split("T")[0] === startDate.split("T")[0] &&
        // Check if class date matches startDate
        cls.status !== "cancelled" && // Exclude cancelled classes
        cls.status !== "rescheduled" // Exclude rescheduled classes
    );
    const events = await Events.find({
      coachId,
      dates: startDate.split("T")[0],
    });
    // Extract startTime and endTime from events and filteredClasses
    let timeArray = [];
    events.forEach((event) => {
      timeArray.push({ startTime: event.startTime, endTime: event.endTime });
    });
    filteredClasses.forEach((cls) => {
      timeArray.push({
        startTime: cls.classStartTime,
        endTime: cls.classEndTime,
      });
    });

    // Sort the timeArray in ascending order based on startTime
    timeArray.sort((a, b) => (a.startTime > b.startTime ? 1 : -1));

    return res.status(200).json(timeArray);
  } catch (e) {
    console.log(e);
    return res
      .status(500)
      .json({ error: "Internal server error: " + e.message });
  }
};

// export const getBookingSlots = async (req, res) => {
//   try {
//     const courseDetails = req.body;
//     if (!courseDetails || !courseDetails.coach_id) {
//       return res
//         .status(404)
//         .json({ error: "Please provide the proper course details" });
//     }
//     const coachDetails = await Coach.findById(courseDetails.coach_id);

//     if (!coachDetails) {
//       return res.status(404).json({ error: "Coach details not found" });
//     }

//     const user = {
//       id: coachDetails._id,
//       email: coachDetails.email,
//       startDate: courseDetails.startDate,
//       endDate: courseDetails.endDate,
//     };
//     const events = await getEventListForBookings(user);
//     if (events.status === 200) {
//       // Filter out the undesired event
//       const filteredEvents = events?.data.filter((event) => {
//         return (
//           event.description &&
//           !event.description.includes(`${courseDetails.course_id}`)
//         );
//       });
//       const eventTimes = filteredEvents.map((event) => ({
//         start: event.start.dateTime.split("T")[1].split("+")[0].slice(0, 5), // Extracting time from dateTime and removing timezone, then taking only the first 5 characters (HH:MM)
//         end: event.end.dateTime.split("T")[1].split("+")[0].slice(0, 5), // Extracting time from dateTime and removing timezone, then taking only the first 5 characters (HH:MM)
//       }));
//       return res.status(200).json(eventTimes);
//     } else {
//       return res.status(400).json(events.error);
//     }
//   } catch (error) {
//     console.log(error);
//     return res.status(500).json({ error: error.message });
//   }
// };

export const updateBookingStatus = async (req, res) => {
  try {
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const { status } = req.body;

    const data = await Booking.findByIdAndUpdate(
      id,
      { status },
      {
        new: true,
        upsert: false,
      }
    );
    if (data) {
      res.status(200).json({ message: "Status updated successfully" });
    } else {
      res.status(400).json({ error: "Booking not found" });
    }
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const cancelBooking = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "Id is required" });
    }
    const { classes, sender, status } = req.body;
    if (classes.length < 1) {
      return res.status(404).json({ error: "Please send the class" });
    }
    const booking = await Booking.findById(id).populate(["player"]);
    const course = await Course.findById(booking.courseId);

    if (course.classType === "course" && course.maxGroupSize > 1) {
      return res.status(404).json({ error: "Can't Cancel a Course" });
    }

    let walletAmount = 0;
    let razorpayAmount = 0;
    let coachAmount = 0;
    const updatedClasses = []; // Store updated classes for status update

    if (sender === "player") {
      for (const bookedClass of classes) {
        // Construct class date and time using the date and startTime of the booked class
        const classDate = new Date(
          `${bookedClass.date.split("T")[0]}T${bookedClass.startTime}:00+05:30`
        );

        // Current date and time in IST
        const currentTime = new Date();
        const timeDifference = classDate - currentTime;
        const hoursDifference = Math.floor(timeDifference / (1000 * 60 * 60));

        let walletRefundAmount = 0;
        let razorpayRefundAmount = 0;
        let amountPaidToCoach = 0;

        if (hoursDifference >= 0 && hoursDifference <= 4) {
          walletRefundAmount = 0; // No refund
          amountPaidToCoach = bookedClass.fees;
        } else if (hoursDifference > 4 && hoursDifference <= 12) {
          walletRefundAmount = bookedClass.fees * 0.5; // 50% refund based on class fees
          amountPaidToCoach = bookedClass.fees * 0.5;
        } else if (hoursDifference > 12 && hoursDifference <= 48) {
          walletRefundAmount = bookedClass.fees; // 100% refund based on class fees
        } else {
          if (booking.wallet) {
            walletRefundAmount = bookedClass.fees; // 100% refund based on class fees
          } else {
            razorpayRefundAmount = bookedClass.fees; // 100% refund based on class fees
          }
        }

        walletAmount += walletRefundAmount; // Add to wallet refund total
        razorpayAmount += razorpayRefundAmount; // Add to Razorpay refund total
        coachAmount += amountPaidToCoach;

        // Update class status and cancellation date for player
        bookedClass.status = status;
        bookedClass.cancellationDate = new Date();
        bookedClass.coachFeesAfterCancelation = coachAmount;
        updatedClasses.push(bookedClass);
      }
    } else {
      for (const bookedClass of classes) {
        // Construct class date and time using the date and startTime of the booked class
        const classDate = new Date(
          `${bookedClass.date.split("T")[0]}T${bookedClass.startTime}:00+05:30`
        );

        // Current date and time in IST
        const currentTime = new Date();
        const timeDifference = classDate - currentTime;
        const hoursDifference = Math.floor(timeDifference / (1000 * 60 * 60));

        let walletRefundAmount = 0;
        let razorpayRefundAmount = 0;

        // Determine refund amount based on hoursDifference
        if (hoursDifference >= 12) {
          walletRefundAmount = bookedClass.fees; // 100% refund based on class fees
        } else {
          if (booking.wallet) {
            walletRefundAmount = bookedClass.fees; // 100% refund based on class fees
          } else {
            razorpayRefundAmount = bookedClass.fees; // 100% refund based on class fees
          }
        }

        walletAmount += walletRefundAmount; // Add to wallet refund total
        razorpayAmount += razorpayRefundAmount; // Add to Razorpay refund total

        // Update class status and cancellation date for player
        bookedClass.coachFeesAfterCancelation = coachAmount;
        bookedClass.status = status;
        bookedClass.cancellationDate = new Date();
        updatedClasses.push(bookedClass);
      }
    }
    // Perform refund transactions
    if (walletAmount > 0) {
      const walletRefundResult = await updateBalance(
        booking.player,
        "credit",
        booking.courseId,
        booking.playerEmail,
        walletAmount
      );
      if (walletRefundResult.status === 500) {
        return res.status(500).json({ error: "Transaction not created" });
      }
    }

    if (razorpayAmount > 0) {
      const payment = await razorpayRefund(
        booking.razorPayPaymentId,
        razorpayAmount
      );
      if (payment.status === 500) {
        return res.status(500).json({ error: "Can't refund amount" });
      }
    }

    // Update booking status
    if (status) {
      for (const updatedClass of updatedClasses) {
        // Find the class in booking and update its status
        const index = booking.classes.findIndex(
          (c) => c._id.toString() === updatedClass._id.toString()
        );
        if (index !== -1) {
          booking.classes[index].status = updatedClass.status;
          booking.classes[index].coachFeesAfterCancelation =
            updatedClass.coachFeesAfterCancelation;
          booking.classes[index].cancellationDate =
            updatedClass.cancellationDate;
        }
      }
      await booking.save();
    }
    // Delete events for classes
    const deletionResult = await deleteSingleEvent(
      booking.coachId,
      updatedClasses
    );
    if (deletionResult.status === 200) {
      if (razorpayAmount > 0) {
        if (booking.player.mobile) {
          await sendSms(
            "664ed8acd6fc0549ab160ed2",
            "4",
            booking.player?.mobile,
            null,
            null
          );
        }
        await sendEmail(
          booking.playerName,
          booking.playerEmail,
          "payment_refund_2",
          `{"User_Name": "${booking.playerName}", "Transaction_ID": "${booking.razorPayPaymentId}", "Amount":"${razorpayAmount}", "Reason":"Cancellation"}`
        );
      }

      if (sender === "player") {
        // booking cancellation player
        // for Player
        await sendEmail(
          booking.playerName,
          booking.playerEmail,
          "booking_cancellation_by_the_player_2",
          `{"User_Name": "${booking.playerName}", "Coach": "${course.coachName}"}`
        );
        // for coach
        await sendEmail(
          course.coachName,
          course.coachEmail,
          "booking_cancellation_by_player_for_the_coach",
          `{"Coach": "${course.coachName}", "player": "${booking.playerName}"}`
        );
      } else {
        // booking cancellation coach
        // for Player
        await sendEmail(
          booking.playerName,
          booking.playerEmail,
          "booking_cancellation_by_coach_for_player",
          `{"player": "${booking.playerName}", "coach": "${course.coachName}"}`
        );
        // for coach
        await sendEmail(
          course.coachName,
          course.coachEmail,
          "booking_cancellation_by_coach_for_coach",
          `{"coach": "${course.coachName}", "player": "${booking.playerName}"}`
        );
      }
      return res.status(200).json({ message: "Booking canceled successfully" });
    } else {
      return res
        .status(deletionResult.status)
        .json({ error: deletionResult.error });
    }
  } catch (e) {
    console.error(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Utility function to calculate duration
const calculateDuration = (startTime, endTime) => {
  const start = moment(startTime, "HH:mm");
  const end = moment(endTime, "HH:mm");
  const duration = moment.duration(end.diff(start));
  const minutes = duration.asMinutes(); // Get duration in minutes
  return `${minutes} mins`;
};

// Generate classes array based on course dates and fees
const generateClassesArray = async (courseId, courseType, classes, coachId) => {
  const coach = await Coach.findById(coachId);
  if (courseType === "course") {
    const fCourse = await Course.findById(courseId);
    const { startDate, endDate, startTime, endTime, days, dates } =
      fCourse.dates;
    const totalDays = moment(endDate).diff(startDate, "days") + 1;
    const feesPerDay = fCourse.fees.feesCourse / dates.length;
    const gstPerDay = (Number(fCourse.fees.feesCourse) * 0.18) / dates.length;
    const totalAmountPaid = coach.hasGst
      ? Number(feesPerDay) + Number(gstPerDay)
      : Number(feesPerDay);
    const generatedClasses = [];
    for (let i = 0; i < totalDays; i++) {
      const currentDate = moment(startDate).add(i, "days");
      const dayOfWeek = moment(currentDate).format("ddd");
      if (days.includes(dayOfWeek)) {
        const classDate = moment(currentDate).format("YYYY-MM-DD");
        const classDetails = {
          date: classDate,
          startTime,
          endTime,
          duration: calculateDuration(startTime, endTime),
          days: dayOfWeek,
          fees: totalAmountPaid,
          coachFeesAfterCancelation: totalAmountPaid,
          invoice: `INV-${uuidv4().replace(/-/g, "").slice(0, 8)}`,
        };
        generatedClasses.push(classDetails);
      }
    }

    return generatedClasses;
  } else {
    const updatedClasses = classes.map((cls) => {
      const feesWithGST = cls.fees + cls.fees * 0.18; // Add 18% GST to the fees
      return {
        ...cls,
        fees: coach.hasGst ? feesWithGST : cls.fees, // Update the fees with GST included
        coachFeesAfterCancelation: coach.hasGst ? feesWithGST : cls.fees,
      };
    });

    return updatedClasses; // Return the updated classes array
  }
};

export const verifyBookingDates = async (params) => {
  try {
    let { courseId, classes, courseType, coachId } = params;
    if (courseType === "class") {
      const conflictingDates = [];
      for (const classInfo of classes) {
        const { date, startTime, endTime } = classInfo;

        const incomingDate = moment.utc(date, "YYYY-MM-DDTHH:mm:ssZ").format(); // Convert incoming date to UTC
        const bookingExists = await Booking.findOne({
          courseId,
          "classes.date": incomingDate,
          "classes.status": { $nin: ["cancelled", "rescheduled"] }, // Exclude classes with status "cancelled" or "rescheduled"
          $or: [
            {
              $and: [
                { "classes.startTime": { $lt: endTime } }, // Changed to $lte
                { "classes.endTime": { $gt: startTime } }, // Changed to $gte
              ],
            },
            {
              $and: [
                { "classes.startTime": { $gt: startTime, $lt: endTime } }, // Changed to $gt and $lt
                { "classes.endTime": { $gt: endTime } },
              ],
            },
            {
              $and: [
                { "classes.startTime": { $lt: startTime } }, // Changed to $lt
                { "classes.endTime": { $gt: startTime, $lt: endTime } }, // Changed to $gt and $lt
              ],
            },
          ],
        });
        if (bookingExists) {
          conflictingDates.push({
            date,
            courseId,
            conflicting: "booking exists",
          });
        }
        const conflictingEvents = await Events.findOne({
          coachId,
          dates: {
            $elemMatch: { $eq: new Date(date).toISOString().split("T")[0] },
          }, // Match only the date part
          $or: [
            {
              $and: [
                { startTime: { $lt: endTime } },
                { endTime: { $gt: startTime } },
              ],
            },
            {
              $and: [
                { startTime: { $gt: startTime, $lt: endTime } },
                { endTime: { $gt: endTime } },
              ],
            },
            {
              $and: [
                { startTime: { $lt: startTime } },
                { endTime: { $gt: startTime, $lt: endTime } },
              ],
            },
          ],
        });

        if (conflictingEvents) {
          conflictingDates.push({
            date,
            courseId,
            conflicting: "event conflict",
          });
        }
      }

      if (conflictingDates.length > 0) {
        return {
          status: 400,
          error: "Conflicting Date and time",
          conflictingDates,
        };
      } else {
        return {
          status: 200,
          message: "Create the class",
        };
      }
    } else {
      const course = await Course.findById(courseId);
      if (course.maxGroupSize === course.playerEnrolled) {
        return {
          status: 400,
          error: "Max group size reached",
        };
      } else {
        return {
          status: 200,
          message: "Create the course",
        };
      }
    }
  } catch (error) {
    return {
      status: 500,
      error: "Internal server error: " + error.message,
    };
  }
};

export const verifyBookingPayment = async (params) => {
  try {
    let { courseId, classes, pricePaid, wallet, player } = params;

    const course = await Course.findById(courseId).populate(
      "coach_id",
      "academy_id"
    );

    if (!course) {
      return {
        status: 404,
        error: "Course not found",
      };
    }
    if (course.status === "inactive" || course.coachStatus === "inactive") {
      return {
        status: 404,
        error: "Course is inactive",
      };
    }
    let verified = false;
    let gstAmount = 0;
    let amountPaid = 0;
    let platformFee = 0;

    if (course.academy_id) {
      const academy = await Academy.findById(course.academy_id);
      if (academy.platformFee) {
        platformFee = academy.platformFee;
      } else {
        platformFee = course.fees.feesCourse * (process.env.PLATFORM_FEE / 100);
      }
    } else {
      platformFee = course.fees.feesCourse * (process.env.PLATFORM_FEE / 100);
    }

    if (course.classType === "course") {
      const feesCourse = course.fees.feesCourse;
      const feesPlatform = feesCourse * (platformFee / 100); // Platform fees as 12% of course fees
      gstAmount = feesPlatform * 0.18; // Course fees + Platform fees + 18% tax
      const coachGst = course?.coach_id?.hasGst ? feesCourse * 0.18 : 0;
      amountPaid = Math.ceil(gstAmount + feesPlatform + feesCourse + coachGst);
      verified = pricePaid == amountPaid;
    } else if (course.classType === "class") {
      let classesFees = 0;
      for (const cls of classes) {
        switch (cls.duration) {
          case "30 mins":
            if (cls.fees === course.fees.fees30) {
              classesFees += cls.fees;
            } else {
              return {
                status: 400,
                error: "Invalid fees for class with duration 30 minutes",
              };
            }
            break;
          case "45 mins":
            if (cls.fees === course.fees.fees45) {
              classesFees += cls.fees;
            } else {
              return {
                status: 400,
                error: "Invalid fees for class with duration 45 minutes",
              };
            }
            break;
          case "60 mins":
            if (cls.fees === course.fees.fees60) {
              classesFees += cls.fees;
            } else {
              return {
                status: 400,
                error: "Invalid fees for class with duration 60 minutes",
              };
            }
            break;
          default:
            return {
              status: 400,
              error: `Invalid duration ${cls.duration}`,
            };
        }
      }
      const feesPlatform = classesFees * (platformFee / 100); // Platform fees as 10% of total classes fees
      gstAmount = feesPlatform * 0.18; // Total classes fees + Platform fees + 18% tax
      const coachGst = course?.coach_id?.hasGst ? classesFees * 0.18 : 0;
      amountPaid = Math.ceil(gstAmount + feesPlatform + classesFees + coachGst);
      verified = pricePaid === amountPaid;
    } else {
      return {
        status: 400,
        error: "Invalid course type",
      };
    }

    if (verified) {
      let walletAmount = 0;
      if (wallet) {
        const playerWallet = await Wallet.findOne({ playerId: player });
        if (!playerWallet) {
          return {
            status: 400,
            error: "Player wallet not found",
          };
        }
        walletAmount = playerWallet.balance;
      }

      return {
        status: 200,
        message: "Payment verified successfully",
        amountPaid,
        walletAmount,
      };
    } else {
      return {
        status: 400,
        error: "Payment verification failed",
        amountPaid,
      };
    }
  } catch (e) {
    return {
      status: 500,
      error: e.message || e.toString(),
    };
  }
};

export const markCourseAttendancePlayer = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { courseId, player } = req.body;
    if (!courseId || !player) {
      return res.status(400).json({ error: "Player and course are required" });
    }

    // Get the current date and time in IST using moment-timezone
    const currentDate = moment.tz("Asia/Kolkata");
    const formattedCurrentDate = currentDate.format("YYYY-MM-DD");

    // Find the booking with the specified courseId, player, and the class time window
    const booking = await Booking.findOne({
      courseId,
      player,
    });
    if (!booking) {
      return res
        .status(404)
        .json({ error: "Class not found for the current time window" });
    }

    // Find the class within the time window
    const classIndex = booking.classes.findIndex(
      (cls) => moment(cls.date).format("YYYY-MM-DD") === formattedCurrentDate
    );

    if (classIndex === -1) {
      return res
        .status(404)
        .json({ error: "Class not found for the current time window" });
    }

    // Convert class start time and end time to IST using moment-timezone
    const classStartTimeIST = moment.tz(
      `${formattedCurrentDate} ${booking.classes[classIndex].startTime}`,
      "Asia/Kolkata"
    );
    const classEndTimeIST = moment.tz(
      `${formattedCurrentDate} ${booking.classes[classIndex].endTime}`,
      "Asia/Kolkata"
    );

    // Calculate 15 minutes before and after the class start time
    const fifteenMinutesBeforeClassStart = classStartTimeIST
      .clone()
      .subtract(15, "minutes");
    const fifteenMinutesAfterClassEnd = classEndTimeIST
      .clone()
      .add(15, "minutes");
    if (
      currentDate.isBefore(fifteenMinutesBeforeClassStart) ||
      currentDate.isAfter(fifteenMinutesAfterClassEnd)
    ) {
      return res.status(400).json({
        error:
          "Attendance can only be marked 15 minutes before or after the class time",
      });
    }

    if (booking.classes[classIndex].attendance === "present") {
      return res.status(400).json({ error: "Attendance already marked " });
    }
    // Update attendance status to "present"
    booking.classes[classIndex].attendance = "present";
    await booking.save();

    return res.status(200).json({ message: "Attendance marked successfully" });
  } catch (e) {
    console.error(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const markAttendancePlayer = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "Id is required" });
    }

    const { Class, player } = req.body;
    if (!Class) {
      return res.status(404).json({ error: "Please send the class" });
    }

    const booking = await Booking.findById(id);
    if (booking.player != player) {
      return res
        .status(404)
        .json({ error: `Player is not enrolled in the ${booking.courseType}` });
    }

    const foundClass = booking.classes.find(
      (item) => item._id.toString() === Class
    );
    if (!foundClass) {
      return res.status(404).json({ error: "Class not found" });
    }

    // Get the current date and time in IST using moment-timezone
    const currentDate = moment.tz("Asia/Kolkata");
    const classStartTimeIST = moment.tz(
      foundClass.startTime,
      "HH:mm",
      "Asia/Kolkata"
    );
    const classEndTimeIST = moment.tz(
      foundClass.endTime,
      "HH:mm",
      "Asia/Kolkata"
    );

    // Calculate 15 minutes before and after the class start time
    const fifteenMinutesBeforeClassStart = classStartTimeIST
      .clone()
      .subtract(15, "minutes");
    const fifteenMinutesAfterClassEnd = classEndTimeIST
      .clone()
      .add(15, "minutes");
    if (
      currentDate.isBefore(fifteenMinutesBeforeClassStart) ||
      currentDate.isAfter(fifteenMinutesAfterClassEnd)
    ) {
      return res.status(400).json({
        error:
          "Attendance can only be marked 15 minutes before or after the class time",
      });
    }

    if (foundClass.attendance === "present") {
      return res.status(400).json({ error: "Attendance already marked " });
    }
    foundClass.attendance = "present";
    await booking.save();

    return res.status(200).json({ message: "Attendance marked successfully" });
  } catch (e) {
    console.error(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const markAttendanceCoach = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { lat, long, Class, bookingId, maxGroupSize, courseId } = req.body;
    if (!lat || !long) {
      return res
        .status(404)
        .json({ error: "Latitude and Longitude are required" });
    }
    if (!maxGroupSize) {
      return res.status(404).json({ error: "max group size is required" });
    }
    if (maxGroupSize === 1) {
      if (!bookingId) {
        return res.status(404).json({ error: "Booking is required" });
      }
      const booking = await Booking.findById(bookingId);
      const course = await Course.findById(booking.courseId);
      const foundClass = booking.classes.find(
        (item) => item._id.toString() === Class
      );
      if (!foundClass) {
        return res.status(404).json({ error: "Class not found" });
      }

      // Get the current date and time in IST using moment-timezone
      const formattedCurrentDate = moment.tz("Asia/Kolkata");

      // Convert class start time and end time to IST using moment-timezone
      const classStartTimeIST = moment.tz(
        `${formattedCurrentDate.format("YYYY-MM-DD")} ${foundClass.startTime}`,
        "Asia/Kolkata"
      );
      const classEndTimeIST = moment.tz(
        `${formattedCurrentDate.format("YYYY-MM-DD")} ${foundClass.endTime}`,
        "Asia/Kolkata"
      );

      // Calculate 15 minutes before and after the class start time
      const fifteenMinutesBeforeClassStart = classStartTimeIST
        .clone()
        .subtract(15, "minutes");
      const fifteenMinutesAfterClassEnd = classEndTimeIST
        .clone()
        .add(15, "minutes");

      if (
        formattedCurrentDate.isBefore(fifteenMinutesBeforeClassStart) ||
        formattedCurrentDate.isAfter(fifteenMinutesAfterClassEnd)
      ) {
        return res.status(400).json({
          error:
            "Attendance can only be marked 15 minutes before or after the class time",
        });
      }

      const withinRadius = checkWithinRadius(
        lat,
        long,
        course.facility.location.coordinates[0],
        course.facility.location.coordinates[1],
        0.7
      );
      if (withinRadius) {
        if (foundClass.coachAttendance === "present") {
          return res.status(400).json({ error: "Attendance already marked" });
        }
        foundClass.coachAttendance = "present";
        await booking.save();
        return res
          .status(200)
          .json({ message: "Attendance marked successfully" });
      } else {
        return res.status(400).json({
          error: "Location not Matched",
        });
      }
    } else {
      const course = await Course.findById(courseId);
      const bookings = await Booking.find({ courseId });

      if (!course || bookings.length === 0) {
        return res.status(404).json({ error: "Course or bookings not found" });
      }

      // Check if location matches
      const withinRadius = checkWithinRadius(
        lat,
        long,
        course.facility.location.coordinates[0],
        course.facility.location.coordinates[1],
        0.7 // 500-meter radius
      );

      if (!withinRadius) {
        return res
          .status(400)
          .json({ error: "Coach is not within the attendance marking radius" });
      }

      // Get the current date and time in IST using moment-timezone
      const formattedCurrentDate = moment.tz("Asia/Kolkata");

      for (const booking of bookings) {
        // Find the class within the time window
        const classIndex = booking.classes.findIndex(
          (cls) =>
            moment(cls.date).format("YYYY-MM-DD") ===
            formattedCurrentDate.format("YYYY-MM-DD")
        );

        if (classIndex !== -1) {
          // Convert class start time and end time to IST using moment-timezone
          const classStartTimeIST = moment.tz(
            `${formattedCurrentDate.format("YYYY-MM-DD")} ${
              booking.classes[classIndex].startTime
            }`,
            "Asia/Kolkata"
          );
          const classEndTimeIST = moment.tz(
            `${formattedCurrentDate.format("YYYY-MM-DD")} ${
              booking.classes[classIndex].endTime
            }`,
            "Asia/Kolkata"
          );

          // Calculate 15 minutes before and after the class start time
          const fifteenMinutesBeforeClassStart = classStartTimeIST
            .clone()
            .subtract(15, "minutes");
          const fifteenMinutesAfterClassEnd = classEndTimeIST
            .clone()
            .add(15, "minutes");

          if (
            formattedCurrentDate.isSameOrAfter(
              fifteenMinutesBeforeClassStart
            ) &&
            formattedCurrentDate.isSameOrBefore(fifteenMinutesAfterClassEnd)
          ) {
            // Update coach attendance status to "present"
            booking.classes[classIndex].coachAttendance = "present";
            await booking.save();
          }
        }
      }
      return res
        .status(200)
        .json({ message: "Attendance marked successfully" });
    }
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" + e.message });
  }
};

function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of the Earth in km
  const dLat = (lat2 - lat1) * (Math.PI / 180); // Convert degrees to radians
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km

  return distance;
}

function checkWithinRadius(lat1, lon1, lat2, lon2, radius) {
  const distance = calculateDistance(lat1, lon1, lat2, lon2);
  return distance <= radius;
}

export const bookingCron = async () => {
  try {
    const currentDate = moment().tz("Asia/Kolkata").startOf("day"); // Get current date at the start of the day
    await Booking.updateMany(
      {
        classes: {
          $elemMatch: {
            status: "upcoming",
            date: { $lt: currentDate.toDate() }, // Compare dates at the start of the day
          },
        },
      },
      { $set: { "classes.$.status": "completed" } } // Update the status of the matched class
    );

    // Find bookings where no class has the status "upcoming" and booking status is not already "Inactive"
    const bookings = await Booking.find({
      status: { $ne: "Inactive" },
      "classes.status": { $ne: "upcoming" },
    });
    // Update booking status if all classes are completed/cancelled/rescheduled
    for (let booking of bookings) {
      const allClassesUpdated = booking.classes.every((cls) =>
        ["completed", "cancelled", "rescheduled"].includes(cls.status)
      );

      if (allClassesUpdated && booking.status !== "Inactive") {
        booking.status = "Inactive";
        await booking.save();
      }
    }
  } catch (error) {
    console.error("Error updating booking status:", error);
  }
};

export const reports = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const {
      startDate,
      endDate,
      paymentStatus,
      page,
      isAcademy = false,
    } = req.query;
    let coachIds = req.query.coachId;
    let academyIds = req.query.academyId;
    if (req.userType === "academy" && req.user?.academyId?._id) {
      academyIds = req.user.academyId._id;
    }
    const limit = 25;
    const currentPage = page ? parseInt(page, 10) : 1;
    const skipValue = (currentPage - 1) * limit;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ error: "Start date and end date are required" });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Building the match stage
    const matchStage = {
      "classes.date": { $gte: start, $lte: end },
    };

    if (req.userType === "academy" && req.user?.academyId?._id) {
      matchStage.academyId = req.user.academyId._id;
    }

    if (coachIds) {
      const coachIdArray = Array.isArray(coachIds)
        ? coachIds.map((id) => new mongoose.Types.ObjectId(id))
        : [new mongoose.Types.ObjectId(coachIds)];
      matchStage.coachId = { $in: coachIdArray };
    }

    if (isAcademy) {
      matchStage.academyId = { $exists: true, $ne: null };
    }

    if (academyIds) {
      const academyIdArray = Array.isArray(academyIds)
        ? academyIds.map((id) => new mongoose.Types.ObjectId(id))
        : [new mongoose.Types.ObjectId(academyIds)];
      matchStage.academyId = { $in: academyIdArray };
    }

    if (paymentStatus) {
      matchStage["classes.paymentStatus"] = paymentStatus;
    }

    const bookings = await Booking.aggregate([
      { $match: matchStage },
      { $unwind: "$classes" },
      { $match: matchStage },
      {
        $lookup: {
          from: "coaches",
          localField: "coachId",
          foreignField: "_id",
          as: "coachDetails",
        },
      },
      {
        $lookup: {
          from: "academies",
          localField: "academyId",
          foreignField: "_id",
          as: "academyDetails",
        },
      },
      { $unwind: { path: "$coachDetails", preserveNullAndEmptyArrays: true } },
      {
        $unwind: { path: "$academyDetails", preserveNullAndEmptyArrays: true },
      },
      {
        $project: {
          classId: "$classes._id",
          bookingId: "$bookingId",
          date: "$classes.date",
          courseName: "$courseName",
          academyId: "$academyDetails._id",
          academyName: "$academyDetails.name",
          coachId: "$coachDetails._id",
          coachName: { $ifNull: ["$coachDetails.firstName", "Unknown Coach"] },
          coachAttendance: "$classes.coachAttendance",
          playerAttendance: "$classes.attendance",
          classFees: "$classes.fees",
          hasGst: { $ifNull: ["$coachDetails.hasGst", false] },
          coachFeesAfterCancelation: "$classes.coachFeesAfterCancelation",
          refundAmount: {
            $subtract: ["$classes.fees", "$classes.coachFeesAfterCancelation"],
          },
          tds: { $multiply: ["$classes.coachFeesAfterCancelation", 0.01] },
          amountReceived: {
            $subtract: [
              "$classes.coachFeesAfterCancelation",
              { $multiply: ["$classes.coachFeesAfterCancelation", 0.01] },
            ],
          },
          paymentStatus: "$classes.paymentStatus",
          invoice: "$classes.invoice",
          academyShare: {
            $ifNull: ["$coachDetails.academyShare", 0],
          },
          coachShare: {
            $ifNull: ["$coachDetails.coachShare", 0],
          },
        },
      },
      { $sort: { date: 1 } },
      { $skip: skipValue },
      { $limit: limit },
    ]);

    const totalResults = await Booking.aggregate([
      { $match: matchStage },
      { $unwind: "$classes" },
      { $match: matchStage },
      { $count: "totalResults" },
    ]);

    const total = totalResults.length > 0 ? totalResults[0].totalResults : 0;

    return res.status(200).json({
      report: bookings,
      totalResults: total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
    });
  } catch (e) {
    return res
      .status(500)
      .json({ error: `Internal server error - ${e.message}` });
  }
};

export const getCards = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }

    const { startDate, endDate, isAcademy = false } = req.query;
    let academyIds = req.query.academyId;

    if (req.userType === "academy" && req.user?.academyId?._id) {
      academyIds = req.user.academyId._id;
    }

    // Building the match stage
    const matchStage = {};

    // Handle date filtering based on provided parameters
    if (startDate && endDate) {
      // Both dates provided - use current logic
      const start = new Date(startDate);
      const end = new Date(endDate);
      matchStage["classes.date"] = { $gte: start, $lte: end };
    } else if (startDate) {
      // Only startDate provided - from startDate to end of time
      const start = new Date(startDate);
      matchStage["classes.date"] = { $gte: start };
    } else if (endDate) {
      // Only endDate provided - from start of time to endDate
      const end = new Date(endDate);
      matchStage["classes.date"] = { $lte: end };
    }
    // If neither date is provided, no date filter is applied (gets all data)

    if (req.userType === "academy" && req.user?.academyId?._id) {
      matchStage.academyId = req.user.academyId._id;
    }

    if (isAcademy) {
      matchStage.academyId = { $exists: true, $ne: null };
    }

    if (academyIds) {
      const academyIdArray = Array.isArray(academyIds)
        ? academyIds.map((id) => new mongoose.Types.ObjectId(id))
        : [new mongoose.Types.ObjectId(academyIds)];
      matchStage.academyId = { $in: academyIdArray };
    }

    const cards = await Booking.aggregate([
      { $match: matchStage },
      { $unwind: "$classes" },
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalPayment: { $sum: "$classes.fees" },
          paymentReceived: {
            $sum: {
              $cond: [
                { $eq: [{ $toLower: "$classes.paymentStatus" }, "paid"] },
                "$classes.fees",
                0,
              ],
            },
          },
          paymentPending: {
            $sum: {
              $cond: [
                { $ne: [{ $toLower: "$classes.paymentStatus" }, "paid"] },
                "$classes.fees",
                0,
              ],
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          totalPayment: { $round: ["$totalPayment", 2] },
          paymentReceived: { $round: ["$paymentReceived", 2] },
          paymentPending: { $round: ["$paymentPending", 2] },
        },
      },
    ]);

    // If no data found, return zeros
    const result =
      cards.length > 0
        ? cards[0]
        : {
            totalPayment: 0,
            paymentReceived: 0,
            paymentPending: 0,
          };

    return res.status(200).json({ cards: result });
  } catch (e) {
    return res
      .status(500)
      .json({ error: `Internal server error - ${e.message}` });
  }
};

export const markPaymentStatusPaid = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { classId } = req.query;
    if (!classId) {
      return res.status(404).json({
        error: "classId is required",
      });
    }
    const booking = await Booking.findOne({ "classes._id": classId });
    if (!booking) {
      return res.status(404).json({
        error: "Booking not found",
      });
    }

    // Find the specific class within the booking
    const classToUpdate = booking.classes.find(
      (cls) => cls._id.toString() === classId
    );
    if (!classToUpdate) {
      return res.status(404).json({
        error: "Class not found in the booking",
      });
    }

    // Update paymentStatus to 'paid' for the specific class
    classToUpdate.paymentStatus = "paid";

    await booking.save();

    return res.status(200).json({
      message: `Payment status updated to 'paid' for class with ID ${classId}`,
      updatedClass: classToUpdate,
    });
  } catch (e) {
    return res.status(500).json({
      error: `Internal server error - ${e}`,
    });
  }
};

const generateCSV = async (
  startDate,
  endDate,
  paymentStatus,
  coachId,
  academyId,
  isAcademy
) => {
  try {
    if (!startDate || !endDate) {
      throw new Error("Start date and end date are required");
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Building the match stage
    const matchStage = {
      "classes.date": { $gte: start, $lte: end },
    };

    if (coachId) {
      const coachIdArray = Array.isArray(coachId)
        ? coachId.map((id) => new mongoose.Types.ObjectId(id))
        : [new mongoose.Types.ObjectId(coachId)];
      matchStage.coachId = { $in: coachIdArray };
    }

    if (isAcademy) {
      matchStage.academyId = { $exists: true, $ne: null };
    }

    if (academyId) {
      const academyIdArray = Array.isArray(academyId)
        ? academyId.map((id) => new mongoose.Types.ObjectId(id))
        : [new mongoose.Types.ObjectId(academyId)];
      matchStage.academyId = { $in: academyIdArray };
    }

    if (paymentStatus) {
      matchStage["classes.paymentStatus"] = paymentStatus;
    }

    const bookings = await Booking.aggregate([
      { $match: matchStage },
      { $unwind: "$classes" },
      { $match: matchStage },
      {
        $lookup: {
          from: "coaches",
          localField: "coachId",
          foreignField: "_id",
          as: "coachDetails",
        },
      },
      {
        $lookup: {
          from: "academies",
          localField: "academyId",
          foreignField: "_id",
          as: "academyDetails",
        },
      },
      { $unwind: { path: "$coachDetails", preserveNullAndEmptyArrays: true } },
      {
        $unwind: { path: "$academyDetails", preserveNullAndEmptyArrays: true },
      },
      {
        $setWindowFields: {
          partitionBy: "$bookingId", // Group by bookingId
          sortBy: { "classes.date": 1 }, // Sort classes by date
          output: {
            classIndex: { $documentNumber: {} }, // Create index for each class
          },
        },
      },
      {
        $project: {
          bookingId: "$bookingId",
          date: "$classes.date",
          courseName: "$courseName",
          academyName: { $ifNull: ["$academyDetails.name", "Unknown Academy"] },
          coachName: { $ifNull: ["$coachDetails.firstName", "Unknown Coach"] },
          coachAttendance: "$classes.coachAttendance",
          playerAttendance: "$classes.attendance",
          "Gst (if applicable)": {
            $cond: {
              if: "$coachDetails.hasGst", // Check if GST is applicable
              then: {
                // Calculate GST portion (18% of the base amount)
                $subtract: [
                  "$classes.fees", // Total fees including GST
                  { $divide: ["$classes.fees", 1.18] }, // Base amount (fees without GST)
                ],
              },
              else: 0, // If GST not applicable, show 0
            },
          },
          "classFees (with GST)": "$classes.fees",
          refund: {
            $subtract: ["$classes.fees", "$classes.coachFeesAfterCancelation"],
          },
          tds: { $multiply: ["$classes.coachFeesAfterCancelation", 0.01] },
          amountReceived: {
            $subtract: [
              "$classes.coachFeesAfterCancelation",
              { $multiply: ["$classes.coachFeesAfterCancelation", 0.01] },
            ],
          },
          paymentStatus: "$classes.paymentStatus",
          // Add the index to the invoice field
          invoice: {
            $concat: [
              "$bookingId",
              "_", // Separator between invoice and index
              { $toString: "$classIndex" }, // Convert index to string and append
            ],
          },
          academyShare: {
            $ifNull: ["$coachDetails.academyShare", 0],
          },
          coachShare: {
            $ifNull: ["$coachDetails.coachShare", 0],
          },
        },
      },
      { $sort: { date: 1 } },
    ]);

    // Convert bookings to CSV
    const fields = [
      "bookingId",
      "date",
      "courseName",
      "academyName",
      "coachName",
      "coachAttendance",
      "playerAttendance",
      "Gst (if applicable)",
      "classFees (with GST)",
      "refund",
      "tds",
      "amountReceived",
      "paymentStatus",
      "invoice",
      "academyShare",
      "coachShare",
    ];
    const json2csvParser = new Parser({ fields });
    const csv = json2csvParser.parse(bookings);

    return csv;
  } catch (e) {
    throw new Error(`Error generating CSV - ${e.message}`);
  }
};

// Express route handler for CSV download
export const downloadCSV = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const {
      startDate,
      endDate,
      paymentStatus,
      coachId,
      academyId,
      isAcademy = false,
    } = req.query;

    if (req.userType === "academy" && req.user?.academyId?._id) {
      academyId = req.user.academyId._id;
    }

    const csv = await generateCSV(
      startDate,
      endDate,
      paymentStatus,
      coachId,
      academyId,
      isAcademy
    );

    // Set headers for CSV download
    res.setHeader("Content-disposition", "attachment; filename=report.csv");
    res.set("Content-Type", "text/csv");
    res.status(200).send(csv);
  } catch (e) {
    console.error(`CSV Download Error: ${e.message}`);
    res.status(500).json({ error: `Internal server error - ${e.message}` });
  }
};

// // Example usage
// const lat1 = 28.584915; // Latitude of point 1
// const lon1 = 77.31165; // Longitude of point 1
// const lat2 = 28.589453; // Latitude of point 2 (new coordinates to check)
// const lon2 = 77.311966; // Longitude of point 2 (new coordinates to check)
// const radius = 0.7; // 1 km radius

// const withinRadius = checkWithinRadius(lat1, lon1, lat2, lon2, radius);
// console.log(withinRadius); // Output: true (if the distance is less than or equal to 1 km)
