import jwt from "jsonwebtoken";
import AcademyUser from "../academyUser/academyUserModal.js";
import { Academy } from "../academy/academyModel.js";
import { SECRETS } from "../../utils/config.js";
import AppError from "../../middlewares/appError.js";
import { getUserGroupAccessScopes } from "../academyUserGroup/academyUserGroupController.js";
import { catchAsync } from "../../utils/helpers.js";

export const verifyToken = (type) =>
  catchAsync(async (req, res, next) => {
    const accessTokenSecret = SECRETS.jwt;
    const refreshTokenSecret = SECRETS.jwtRefresh;
    // Check header first, then check cookie

    const token =
      req.headers.authorization?.split(" ")[1] || req.cookies[`${type}Token`];

    if (!token) {
      return next(
        new AppError("Token is missing", 401, {
          errors: [{ field: "token", message: "Token is missing" }],
        })
      );
    }

    const decoded = jwt.verify(
      token,
      type === "access" ? accessTokenSecret : refreshTokenSecret
    );
    req.user = decoded;
    req.refreshToken = type === "refresh" ? token : null;
    next();
  });



// Middleware to check specific module access
export const checkAcademyUserAccess = (module, permission, isPublic = false) => {
  return (req, res, next) => {
    if (!req.accessScopes) {
      if (isPublic) {
        return next();
      } else {
        return next(new AppError("Access scopes not found", 403));
      }
    }

    const moduleScopes = req.accessScopes[module];
    if (!moduleScopes || !moduleScopes.includes(permission)) {
      return next(
        new AppError(
          `Access denied. Required permission: ${permission} for module: ${module}`,
          403
        )
      );
    }

    next();
  };
};


export const academyAccessControl = catchAsync(async (req, res, next) => {
  const academyId = req.params.id || req.params.academyId;

  if (!academyId) {
    return next(new AppError("Academy ID is required", 400));
  }
  if (req.auth && req.auth.userType === "Admin"|| req.userType === "Admin") {
    return next();
  }

if (req.user && req.user.academyId) {
  const userAcademyId =
    typeof req.user.academyId === "object" && req.user.academyId._id
      ? req.user.academyId._id.toString()
      : req.user.academyId.toString();

  if (userAcademyId === academyId.toString()) {
    console.log("Academy user access granted - own academy");
    return next();
  }

  return next(
    new AppError(
      "Access denied. You can only access your own academy data",
      403
    )
  );

  }

  return next(new AppError("Authentication required", 401));
});








