import Joi from "joi";

export const addTopCoachSchema = Joi.object({
  documents: Joi.array()
    .items(
      Joi.object({
        coach: Joi.string().required(),
        position: Joi.number().optional(),
      })
    )
    .max(15)
    .required(),
});

export const updateCoachPositionSchema = Joi.object({
  updates: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        newPosition: Joi.number().required(),
      })
    )
    .required(),
});

// ==================== COURSE VALIDATION SCHEMAS ====================

export const addTopCourseSchema = Joi.object({
  documents: Joi.array()
    .items(
      Joi.object({
        course: Joi.string().required(),
        position: Joi.number().optional(),
      })
    )
    .max(15)
    .required(),
});

export const updateCoursePositionSchema = Joi.object({
  updates: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        newPosition: Joi.number().required(),
      })
    )
    .required(),
});

// ==================== ACADEMY DESCRIPTION VALIDATION SCHEMA ====================

export const academyDescriptionSchema = Joi.object({
  description: Joi.string().min(10).max(5000).required().messages({
    "string.min": "Description must be at least 10 characters long",
    "string.max": "Description cannot exceed 5000 characters",
    "any.required": "Description is required",
  }),
});
