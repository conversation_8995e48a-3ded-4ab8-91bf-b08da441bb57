import AcademyUserGroup from "./academyUserGroupModal.js";
import { catchAsync } from "../../utils/helpers.js";
import AppError from "../../middlewares/appError.js";
import AppSuccess from "../../middlewares/appSuccess.js";

// Helper function to create default admin access scopes
export const createDefaultAdminAccessScopes = () => {
  return {
    dashboard: ["read", "write", "delete"],
    coach: ["read", "write", "delete"],
    course: ["read", "write", "delete"],
    user: ["read", "write", "delete"],
    user_group: ["read", "write", "delete"],
    finances: ["read", "write", "delete"],
    player: ["read", "write", "delete"],
    cms: ["read", "write", "delete"],
    contact: ["read", "write", "delete"],
  };
};

// Helper function to create default admin user group
export const createDefaultAcademyAdminUserGroup = async (academyId) => {
  try {
    const adminUserGroup = new AcademyUserGroup({
      academyId,
      name: "New Admin",
      description: "Default admin group with full access to all modules",
      access_scopes: createDefaultAdminAccessScopes(),
    });
    // Return the user group object without saving - let the caller handle the session
    return adminUserGroup;
  } catch (error) {
    throw new AppError("Failed to create default admin user group", 500);
  }
};

// Create Academy User Group
export const createAcademyUserGroup = catchAsync(async (req, res, next) => {
  const academyId = req.user.academyId._id;
  const { name, description, access_scopes } = req.body;
  const existingGroup = await AcademyUserGroup.findOne({ academyId, name });
  if (existingGroup) {
    throw new AppError(
      "User group with this name already exists for this academy",
      400,
      {
        errors: [
          {
            field: "name",
            message:
              "User group with this name already exists for this academy.",
          },
        ],
      }
    );
  }
  const academyUserGroup = new AcademyUserGroup({
    academyId,
    name,
    description,
    access_scopes: access_scopes,
  });

  const savedUserGroup = await academyUserGroup.save();
  return new AppSuccess(res, {
    message: "Academy user group created successfully",
    data: savedUserGroup,
    statusCode: 201,
  });
});

// Get All Academy User Groups
export const getAllAcademyUserGroups = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, search } = req.query;
  const academyId = req.user.academyId._id;

  const query = { academyId };
  if (search) {
    query.name = { $regex: search, $options: "i" };
  }

  const userGroups = await AcademyUserGroup.find(query)
    .populate("academyId", "name email")
    .limit(Number(limit))
    .skip((Number(page) - 1) * Number(limit))
    .sort({ createdAt: -1 });

  const total = await AcademyUserGroup.countDocuments(query);

  return new AppSuccess(res, {
    message: "All academy user groups retrieved successfully",
    data: {
      userGroups,
      totalResults: total,
      length: userGroups.length,
    },
    statusCode: 200,
  });
});

// Get Academy User Group by ID
export const getAcademyUserGroupById = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const academyId = req.user.academyId._id;
  const userGroup = await AcademyUserGroup.findOne({
    _id: id,
    academyId,
  }).populate("academyId", "name email");

  if (!userGroup) {
    throw new AppError("Academy user group not found", 404, {
      errors: [
        {
          field: "id",
          message:
            "Academy user group not found or does not belong to your academy.",
        },
      ],
    });
  }
  return new AppSuccess(res, {
    message: "Academy user group retrieved successfully",
    data: userGroup,
    statusCode: 200,
  });
});

// Update Academy User Group
export const updateAcademyUserGroup = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { name, description, access_scopes } = req.body;
  const userGroup = await AcademyUserGroup.findById(id);
  if (!userGroup) {
    throw new AppError("Academy user group not found", 404, {
      errors: [
        {
          field: "id",
          message: "Academy user group not found.",
        },
      ],
    });
  }

  // Check if name is being changed and if it conflicts
  if (name && name !== userGroup.name) {
    const userAcademyId = userGroup.academyId;
    const academyId = req.user.academyId._id;
    if (userAcademyId.toString() !== academyId.toString()) {
      throw new AppError("User not authorized to update this user group", 403, {
        errors: [
          {
            field: "academyId_token",
            message: "User not authorized to update this user group.",
          },
        ],
      });
    }
    const existingGroup = await AcademyUserGroup.findOne({
      academyId: userAcademyId,
      name,
      _id: { $ne: id },
    });
    if (existingGroup) {
      throw new AppError(
        "User group with this name already exists for this academy",
        400,
        {
          errors: [
            {
              field: "name",
              message:
                "User group with this name already exists for this academy.",
            },
          ],
        }
      );
    }
  }
  const updatedUserGroup = await AcademyUserGroup.findByIdAndUpdate(
    id,
    { name, description, access_scopes },
    { new: true, runValidators: true }
  ).populate("academyId", "name email");

  return new AppSuccess(res, {
    message: "Academy user group updated successfully",
    data: updatedUserGroup,
    statusCode: 200,
  });
});

// Delete Academy User Group
export const deleteAcademyUserGroup = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const userGroup = await AcademyUserGroup.findById(id);
  if (!userGroup) {
    throw new AppError("Academy user group not found", 404, {
      errors: [
        {
          field: "id",
          message: "Academy user group not found.",
        },
      ],
    });
  }
  const userAcademyId = userGroup.academyId;
  const academyId = req.user.academyId._id;
  if (userAcademyId.toString() !== academyId.toString()) {
    throw new AppError("User not authorized to delete this user group", 403, {
      errors: [
        {
          field: "academyId_token",
          message: "User not authorized to delete this user group.",
        },
      ],
    });
  }
  if (userGroup.name === "New Admin") {
    throw new AppError("Cannot delete the default admin user group", 400, {
      errors: [
        {
          field: "name",
          message: "Cannot delete the default admin user group.",
        },
      ],
    });
  }
  await AcademyUserGroup.findByIdAndDelete(id);
  return new AppSuccess(res, {
    message: "Academy user group deleted successfully",
    statusCode: 200,
  });
});

// Helper function to get user group access scopes
export const getUserGroupAccessScopes = async (userGroupIds) => {
  try {
    const userGroups = await AcademyUserGroup.find({
      _id: { $in: userGroupIds },
    }).select("access_scopes");

    // Merge all access scopes
    const mergedScopes = {};
    userGroups.forEach((group) => {
      Object.keys(group.access_scopes).forEach((module) => {
        if (!mergedScopes[module]) {
          mergedScopes[module] = [];
        }
        // Merge permissions and remove duplicates
        const permissions = [
          ...mergedScopes[module],
          ...group.access_scopes[module],
        ];
        mergedScopes[module] = [...new Set(permissions)];
      });
    });

    return mergedScopes;
  } catch (error) {
    throw new AppError("Failed to get user group access scopes", 500);
  }
};
