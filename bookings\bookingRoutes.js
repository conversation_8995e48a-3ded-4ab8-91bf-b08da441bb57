import { Router } from "express";

import {
  getBookings,
  getBookingDetailsById,
  getBookingSlots,
  cancelBooking,
  markCourseAttendancePlayer,
  markAttendancePlayer,
  markAttendanceCoach,
  reports,
  markPaymentStatusPaid,
  downloadCSV,
  getCards,
  // generateOtpPlayer,
} from "./bookingController.js";
import { coachProtect, flexibleAuth, playerProtect } from "../utils/auth.js";

const router = Router();

router
  .route("/")
  .get(
    flexibleAuth("booking", "read"),
    coachProtect,
    playerProtect,
    getBookings
  );

router.route("/cards").get(flexibleAuth("reports", "read"), getCards);

router
  .route("/:id")
  .get(
    flexibleAuth("booking", "read"),
    coachProtect,
    playerProtect,
    getBookingDetailsById
  );
router
  .route("/events")
  .post(flexibleAuth("booking", "read"), playerProtect, getBookingSlots);
router
  .route("/cancel/:id")
  .post(
    flexibleAuth("booking", "write"),
    coachProtect,
    playerProtect,
    cancelBooking
  );

// router
//   .route("/generateOtpPlayer")
//   .post(flexibleAuth("booking", "read"), generateOtpPlayer);

router
  .route("/attendance/:id")
  .post(flexibleAuth("booking", "write"), playerProtect, markAttendancePlayer);
router
  .route("/markAttendance/")
  .post(
    flexibleAuth("booking", "write"),
    playerProtect,
    markCourseAttendancePlayer
  );
router
  .route("/markAttendanceCoach/")
  .post(flexibleAuth("booking", "write"), coachProtect, markAttendanceCoach);
router
  .route("/reports")
  .post(flexibleAuth("reports", "read"), coachProtect, reports);

router
  .route("/downloadReports")
  .post(flexibleAuth("reports", "read"), coachProtect, downloadCSV);

router
  .route("/markPaymentStatusPaid")
  .post(flexibleAuth("reports", "write"), markPaymentStatusPaid);

export default router;
