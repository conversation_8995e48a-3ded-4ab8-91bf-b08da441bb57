import cors from "cors";

const corsOptions = {
  origin: [
    "http://localhost:3002/",
    "http://localhost:3000",
    "http://localhost:3002",
    "https://sxwcvemsqf.execute-api.ap-south-1.amazonaws.com/",
    "https://sxwcvemsqf.execute-api.ap-south-1.amazonaws.com",
    "https://kdfqfiggp1.execute-api.ap-south-1.amazonaws.com/",
    "https://kdfqfiggp1.execute-api.ap-south-1.amazonaws.com",
    "https://hdorno8zdg.execute-api.ap-south-1.amazonaws.com/",
    "https://hdorno8zdg.execute-api.ap-south-1.amazonaws.com",
    "https://eysjvfcbfa.execute-api.ap-south-1.amazonaws.com/",
    "https://eysjvfcbfa.execute-api.ap-south-1.amazonaws.com",
  ],
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true,
};

export default cors(corsOptions);
