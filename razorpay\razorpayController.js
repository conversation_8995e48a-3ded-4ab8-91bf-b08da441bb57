import Razorpay from "razorpay";
import crypto from "crypto";
import axios from "axios";
import {
  createDraftBooking,
  verifyBookingPayment,
  createBooking,
  verifyBookingDates,
} from "../bookings/bookingController.js";
import { DraftBooking } from "../bookings/draftBookingModal.js";
import { SECRETS } from "../utils/config.js";
import { sendEmail, sendSms } from "../utils/msg.js";
import { Player } from "../player/playerModal.js";
const instance = new Razorpay({
  key_id: SECRETS.razorPayKeyId,
  key_secret: SECRETS.razorPayKeySecret,
});

// When a user pay this function hits
export const checkout = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { pricePaid, wallet } = req.body;
    

    const verifiedDate = await verifyBookingDates(req.body); // verification if the date is available
    if (verifiedDate.status !== 200) {
      return res.status(verifiedDate.status).json({
        error: verifiedDate.error,
        conflictingDate: verifiedDate.conflictingDates,
      });
    }

    // Verify payment
    const paymentVerification = await verifyBookingPayment(req.body); // verify if the payment is correct
    if (paymentVerification.status !== 200) {
      return res.status(400).json({ error: paymentVerification.error });
    }

    // Get wallet amount from payment verification result here we get wallet amount if the user has selected the wallet payment method
    //  if not it will be set to 0 with creating the full payment with razorpay
    const walletAmount = paymentVerification.walletAmount || 0;
    const razorpayAmount = (pricePaid - walletAmount) * 100; // Convert remaining amount to smallest currency unit

    if (razorpayAmount > 0) {
      // Create Razorpay order if amount is positive
      const orderOptions = {
        amount: razorpayAmount,
        currency: "INR",
      };
      const order = await instance.orders.create(orderOptions);
      const booking = await createDraftBooking(
        req.body,
        order.id,
        wallet,
        pricePaid - razorpayAmount / 100
      );
      if (booking.status === 200) {
        return res.status(200).json({ success: true, data: order, booking });
      } else {
        return res.status(400).json({ error: booking.error });
      }
    } else {
      // if all the payment is from wallet create the booking and delete draft booking
      const draftBooking = await createDraftBooking(
        req.body,
        null,
        wallet,
        pricePaid
      );
      const booking = await createBooking(draftBooking.data);
      if (booking.status === 200) {
     
        await DraftBooking.findByIdAndDelete(draftBooking.data._id);
        return res
          .status(200)
          .json({ success: true, data: { order: "Not Required" }, booking });
      } else {
        return res.status(400).json({ error: booking.error });
      }
    }
    
  } catch (error) {
    console.log(error, "checkout Razorpay error");
    return res.status(500).json({ error: "Internal Server error" });
  }
};

// verify the payment after successfully paying the amount through razorpay
export const paymentVerification = async (req, res) => {
  let getPlayer;
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { razorpay_payment_id, razorpay_order_id, razorpay_signature } =
      req.body;
    let body = razorpay_order_id + "|" + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac("sha256", SECRETS.razorPayKeySecret)
      .update(body.toString())
      .digest("hex");

    const isAuthenticatedSignature = expectedSignature === razorpay_signature;
    const url = `https://api.razorpay.com/v1/payments/${razorpay_payment_id}`;
    const paymentDetails = await axios.get(url, {
      auth: {
        username: SECRETS.razorPayKeyId,
        password: SECRETS.razorPayKeySecret,
      },
    });
    if (isAuthenticatedSignature) {
      const draftBooking = await DraftBooking.findOneAndUpdate(
        { orderId: paymentDetails.data.order_id },
        {
          $set: {
            razorPayPaymentId: razorpay_payment_id,
            paymentMode: paymentDetails.data.method,
            paymentStatus: "Success",
            paymentId:
              paymentDetails.data.method === "upi"
                ? paymentDetails.data.upi.vpa
                : paymentDetails.data.method === "card"
                ? paymentDetails.data.card_id
                : paymentDetails.data.method === "wallet"
                ? paymentDetails.data.wallet
                : paymentDetails.data.method === "netbanking"
                ? paymentDetails.data.bank
                : "",
          },
        },
        { new: true }
      );
      const booking = await createBooking(draftBooking);
     getPlayer = await Player.findOne({_id: booking.data.player});
      if (booking.status === 200) {
        await DraftBooking.findOneAndDelete({
          orderId: paymentDetails.data.order_id,
        });
      }

      await sendSms(
        "664ed72fd6fc052ff22ed432",
        "4",
        getPlayer.mobile,
        booking.data.pricePaid,
        paymentDetails.data.method,
        false
      );
      // Booking Confirmation

      return res.status(200).json(booking.data);
    } else {
      const draftBooking = await DraftBooking.findOne({
        orderId: paymentDetails.data.order_id,
      }).populate(["player"]);

      // Payment Failure
      if (draftBooking.player.mobile) {
        await sendSms(
          "664ed8fbd6fc057e39771cc8",
          "4",
          draftBooking.player?.mobile,
          null,
          null
        );
      }
      await sendEmail(
        draftBooking.playerName,
        draftBooking.playerEmail,
        "payment_unsuccessful_2",
        `{"User_Name": "${draftBooking.playerName}", "Invoice_ID":"${draftBooking.bookingId}" , "Amount": "${draftBooking.pricePaid}", "Reason": "Unauthorized Payment"}`
      );

      await DraftBooking.findOneAndDelete({
        orderId: paymentDetails.data.order_id,
      });

      return res
        .status(400)
        .json({ success: false, message: "Unauthorize payment" });
    }
  } catch (e) {
    console.log(e, "error");
    return res.status(500).json({ error: "Internal server Error" });
  }
};

// razorpay refund caller function
export const razorpayRefund = async (id, amount) => {
  try {
    const refund = await instance.payments.refund(id, { amount: amount * 100 });
    return {
      status: 200,
      data: refund,
    };
  } catch (err) {
    return {
      status: 500,
      error: err,
    };
  }
};

export const getPaymentDetails = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const paymentId = req.params.id;
    if (!paymentId) {
      return res.status(400).json({ error: "Please provide the payment id" });
    }
    const url = `https://api.razorpay.com/v1/payments/${paymentId}`;
    const paymentDetails = await axios.get(url, {
      auth: {
        username: SECRETS.razorPayKeyId,
        password: SECRETS.razorPayKeySecret,
      },
    });
    return res.status(200).json({ paymentDetails: paymentDetails.data });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// test function to create payment with api (Not using this )
export const createPayment = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { amount, currency, order_id, payment_capture } = req.body;

    const data = JSON.stringify({
      amount,
      currency,
      receipt: `receipt_order_${order_id}`,
      payment_capture,
      notes: {
        order_id,
      },
    });

    const authToken = Buffer.from(
      `${SECRETS.razorPayKeyId}:${SECRETS.razorPayKeySecret}`
    ).toString("base64");

    const config = {
      method: "post",
      maxBodyLength: Infinity,
      url: "https://api.razorpay.com/v1/payments",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${authToken}`,
      },
      data,
    };

    const response = await axios.request(config);
    res.status(200).json(response.data);
  } catch (error) {
    console.log(error.response.data);
    res.status(500).json({ error: "Internal server error" });
  }
};
