import { config } from "dotenv";
config();
const SECRETS = {
  mongoUrl: process.env.MONGO_CONNECTION_STRING,
  port: process.env.PORT,
  jwt: process.env.JWT_SECRET,
  jwtRefresh: process.env.JWT_REFRESH_SECRET,
  jwtExp: "7d",
  jwtRefreshExp: "30d",
  academyUserRefreshTokenPath: "/api/academy/update-refresh-access",
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
  AWS_BUCKET_NAME: process.env.AWS_BUCKET_NAME,
  AWS_S3_REGION: process.env.AWS_S3_REGION,
  calendarApiKey: process.env.API_KEY_CALENDAR,
  calendarClientId: process.env.clientID,
  calendarClientSecret: process.env.clientSecret,
  calendarRedirectUrl: process.env.REDIRECT_URL,
  frontEndBaseUrl: process.env.COACH_FRONTEND_BASE_URL,
  playerBaseUrl: process.env.PLAYER_FRONTEND_BASE_URL,
  razorPayKeyId: process.env.RAZORPAY_KEY_ID,
  razorPayKeySecret: process.env.RAZORPAY_KEY_SECRET,
  superAdmin: process.env.SUPER_ADMIN,
  msgAuthKey: process.env.MSG_AUTH_KEY,
  senderName: process.env.SENDER_NAME,
  senderEmail: process.env.SENDER_EMAIL,
  senderDomain: process.env.SENDER_DOMAIN,
  platformFee: process.env.PLATFORM_FEE
};

export { SECRETS };
